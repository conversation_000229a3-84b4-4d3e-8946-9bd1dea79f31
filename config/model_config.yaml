# Model Configuration for Loan Default Prediction

# Model Settings
model:
  name: "Loan Default Prediction Model"
  version: "1.0.0"
  type: "decision_tree"
  
  # Business Requirements
  target_default_rate: 0.08  # Target: <8% default rate
  target_approval_rate: 0.70  # Target: >70% approval rate
  
  # Model Parameters
  decision_tree:
    max_depth: 7
    min_samples_split: 5
    min_samples_leaf: 2
    criterion: "gini"
    random_state: 42
  
  random_forest:
    n_estimators: 100
    max_depth: 7
    min_samples_split: 5
    min_samples_leaf: 2
    random_state: 42
  
  logistic_regression:
    C: 1.0
    penalty: "l2"
    solver: "liblinear"
    random_state: 42

# Feature Configuration
features:
  # Numerical features
  numerical:
    - "LOAN"      # Loan amount
    - "MORTDUE"   # Amount due on existing mortgage
    - "VALUE"     # Property value
    - "YOJ"       # Years on current job
    - "DEROG"     # Number of derogatory reports
    - "DELINQ"    # Number of delinquent credit lines
    - "CLAGE"     # Age of oldest credit line (months)
    - "NINQ"      # Number of recent credit inquiries
    - "CLNO"      # Number of credit lines
    - "DEBTINC"   # Debt-to-income ratio
  
  # Categorical features
  categorical:
    - "REASON"    # Loan purpose (HomeImp, DebtCon)
    - "JOB"       # Job category
  
  # Target variable
  target: "BAD"   # 1 = Default, 0 = No Default

# Data Processing
preprocessing:
  # Missing value handling
  missing_values:
    numerical_strategy: "median"
    categorical_strategy: "mode"
  
  # Feature scaling
  scaling:
    method: "standard"  # StandardScaler
    
  # Encoding
  encoding:
    categorical_method: "label"  # LabelEncoder

# Prediction Thresholds
thresholds:
  default: 0.5        # Default classification threshold
  conservative: 0.3   # Conservative threshold (higher approval rate)
  strict: 0.7        # Strict threshold (lower default rate)

# Evaluation Metrics
evaluation:
  primary_metric: "f1_score"
  
  # Metrics to track
  metrics:
    - "accuracy"
    - "precision"
    - "recall"
    - "f1_score"
    - "roc_auc"
  
  # Business metrics
  business_metrics:
    - "default_rate"
    - "approval_rate"
    - "true_positive_rate"
    - "false_positive_rate"

# Regulatory Compliance
compliance:
  # Equal Credit Opportunity Act (ECOA) compliance
  ecoa:
    prohibited_features: []  # Features that should not be used for fairness
    
  # Model interpretability requirements
  interpretability:
    required: true
    max_tree_depth: 10  # For decision trees
    
  # Documentation requirements
  documentation:
    model_card: true
    performance_report: true
    bias_analysis: true
