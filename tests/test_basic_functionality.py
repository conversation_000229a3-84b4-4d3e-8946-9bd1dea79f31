"""
Basic functionality tests for loan default prediction.
"""

import pytest
import pandas as pd
import numpy as np
import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent / 'src'))

from data_preprocessing import LoanDataPreprocessor
from model_utils import LoanDefaultModel


class TestDataPreprocessing:
    """Test data preprocessing functionality."""
    
    def test_preprocessor_initialization(self):
        """Test preprocessor initialization."""
        preprocessor = LoanDataPreprocessor()
        assert preprocessor.scaler is not None
        assert preprocessor.label_encoders == {}
        assert preprocessor.feature_columns is None
        assert preprocessor.target_column == 'BAD'
    
    def test_missing_value_handling(self):
        """Test missing value handling."""
        # Create sample data with missing values
        data = pd.DataFrame({
            'numerical_col': [1, 2, np.nan, 4, 5],
            'categorical_col': ['A', 'B', None, 'A', 'B'],
            'BAD': [0, 1, 0, 1, 0]
        })
        
        preprocessor = LoanDataPreprocessor()
        processed_data = preprocessor.handle_missing_values(data.copy())
        
        # Check that no missing values remain
        assert processed_data.isnull().sum().sum() == 0
        
        # Check that numerical column was filled with median
        expected_median = data['numerical_col'].median()
        assert processed_data.loc[2, 'numerical_col'] == expected_median
    
    def test_categorical_encoding(self):
        """Test categorical feature encoding."""
        data = pd.DataFrame({
            'categorical_col': ['A', 'B', 'A', 'C', 'B'],
            'BAD': [0, 1, 0, 1, 0]
        })
        
        preprocessor = LoanDataPreprocessor()
        encoded_data = preprocessor.encode_categorical_features(data.copy(), fit=True)
        
        # Check that categorical column is now numerical
        assert pd.api.types.is_numeric_dtype(encoded_data['categorical_col'])
        
        # Check that label encoder was created
        assert 'categorical_col' in preprocessor.label_encoders


class TestModelUtils:
    """Test model utilities."""
    
    def test_model_initialization(self):
        """Test model initialization."""
        model = LoanDefaultModel('decision_tree')
        assert model.model_type == 'decision_tree'
        assert model.model is None
        assert model.is_trained is False
    
    def test_invalid_model_type(self):
        """Test invalid model type raises error."""
        with pytest.raises(ValueError):
            model = LoanDefaultModel('invalid_model')
            model._get_model()
    
    def test_model_training(self):
        """Test model training with sample data."""
        # Create sample training data
        np.random.seed(42)
        X = pd.DataFrame(np.random.randn(100, 5), columns=[f'feature_{i}' for i in range(5)])
        y = pd.Series(np.random.randint(0, 2, 100))
        
        model = LoanDefaultModel('decision_tree')
        model.train(X, y, max_depth=3)
        
        assert model.is_trained is True
        assert model.model is not None
    
    def test_prediction_without_training(self):
        """Test that prediction fails without training."""
        model = LoanDefaultModel('decision_tree')
        X = pd.DataFrame(np.random.randn(10, 5))
        
        with pytest.raises(ValueError):
            model.predict(X)


class TestIntegration:
    """Integration tests."""
    
    def test_full_pipeline(self):
        """Test the full preprocessing and training pipeline."""
        # Create sample data
        np.random.seed(42)
        data = pd.DataFrame({
            'LOAN': np.random.randint(1000, 50000, 100),
            'VALUE': np.random.randint(50000, 200000, 100),
            'REASON': np.random.choice(['HomeImp', 'DebtCon'], 100),
            'JOB': np.random.choice(['Office', 'Other', 'Sales'], 100),
            'YOJ': np.random.uniform(0, 20, 100),
            'BAD': np.random.randint(0, 2, 100)
        })
        
        # Add some missing values
        data.loc[0:5, 'VALUE'] = np.nan
        data.loc[10:15, 'REASON'] = None
        
        # Test preprocessing
        preprocessor = LoanDataPreprocessor()
        X, y = preprocessor.preprocess_training_data(data.copy())
        
        assert X.shape[0] == 100  # Same number of rows
        assert y.shape[0] == 100
        assert X.isnull().sum().sum() == 0  # No missing values
        
        # Test model training
        model = LoanDefaultModel('decision_tree')
        model.train(X, y, max_depth=3)
        
        assert model.is_trained is True
        
        # Test prediction
        predictions = model.predict(X[:10])
        probabilities = model.predict_proba(X[:10])
        
        assert len(predictions) == 10
        assert probabilities.shape == (10, 2)
        assert all(pred in [0, 1] for pred in predictions)
        assert all(0 <= prob <= 1 for prob_pair in probabilities for prob in prob_pair)


def test_data_file_exists():
    """Test that the main data file exists."""
    data_path = Path(__file__).parent.parent / 'data' / 'loan_default.csv'
    assert data_path.exists(), "Main data file should exist"


def test_config_files_exist():
    """Test that configuration files exist."""
    config_dir = Path(__file__).parent.parent / 'config'
    
    assert (config_dir / 'training_config.yaml').exists()
    assert (config_dir / 'model_config.yaml').exists()


def test_sample_input_exists():
    """Test that sample input file exists."""
    sample_path = Path(__file__).parent.parent / 'data' / 'sample_input.json'
    assert sample_path.exists(), "Sample input file should exist"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
