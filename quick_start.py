#!/usr/bin/env python3
"""
Quick start script for loan default prediction.
Demonstrates the main functionality with minimal setup.
"""

import os
import sys
import pandas as pd
import numpy as np
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent / 'src'))

from data_preprocessing import LoanDataPreprocessor, split_data
from model_utils import LoanDefaultModel, print_evaluation_results
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def quick_demo():
    """Run a quick demonstration of the loan default prediction system."""
    
    print("🚀 Loan Default Prediction - Quick Start Demo")
    print("=" * 60)
    
    # Check if data file exists
    data_path = "data/loan_default.csv"
    if not os.path.exists(data_path):
        print("❌ Data file not found. Please ensure data/loan_default.csv exists.")
        return
    
    try:
        # 1. Load and preprocess data
        print("\n📊 Step 1: Loading and preprocessing data...")
        preprocessor = LoanDataPreprocessor()
        data = preprocessor.load_data(data_path)
        
        print(f"   • Loaded {len(data)} loan applications")
        print(f"   • Features: {list(data.columns)}")
        print(f"   • Default rate: {data['BAD'].mean():.1%}")
        
        # Preprocess data
        X, y = preprocessor.preprocess_training_data(data)
        print(f"   • Processed features shape: {X.shape}")
        
        # 2. Split data
        print("\n🔄 Step 2: Splitting data...")
        X_train, X_test, y_train, y_test = split_data(X, y, test_size=0.2, random_state=42)
        print(f"   • Training set: {X_train.shape[0]} samples")
        print(f"   • Test set: {X_test.shape[0]} samples")
        
        # 3. Train model
        print("\n🤖 Step 3: Training decision tree model...")
        model = LoanDefaultModel('decision_tree')
        model.train(X_train, y_train, max_depth=7, min_samples_split=5, min_samples_leaf=2)
        print("   • Model training completed!")
        
        # 4. Evaluate model
        print("\n📈 Step 4: Evaluating model performance...")
        evaluation_results = model.evaluate(X_test, y_test)
        print_evaluation_results(evaluation_results)
        
        # 5. Feature importance
        print("\n🔍 Step 5: Feature importance analysis...")
        feature_importance = model.get_feature_importance(preprocessor.feature_columns)
        if feature_importance is not None:
            print("   Top 5 most important features:")
            for i, (_, row) in enumerate(feature_importance.head(5).iterrows()):
                print(f"   {i+1}. {row['feature']}: {row['importance']:.3f}")
        
        # 6. Sample predictions
        print("\n🎯 Step 6: Making sample predictions...")
        
        # Create sample loan applications
        sample_applications = [
            {
                'LOAN': 25000, 'VALUE': 150000, 'MORTDUE': 85000,
                'REASON': 'HomeImp', 'JOB': 'Office', 'YOJ': 5.0,
                'DEROG': 0, 'DELINQ': 0, 'CLAGE': 120.5,
                'NINQ': 1, 'CLNO': 12, 'DEBTINC': 35.2
            },
            {
                'LOAN': 50000, 'VALUE': 100000, 'MORTDUE': 95000,
                'REASON': 'DebtCon', 'JOB': 'Other', 'YOJ': 2.0,
                'DEROG': 2, 'DELINQ': 1, 'CLAGE': 80.0,
                'NINQ': 3, 'CLNO': 8, 'DEBTINC': 45.0
            }
        ]
        
        for i, app in enumerate(sample_applications, 1):
            # Convert to DataFrame and preprocess
            app_df = pd.DataFrame([app])
            X_sample = preprocessor.preprocess_prediction_data(app_df)
            
            # Make prediction
            prediction = model.predict(X_sample)[0]
            probability = model.predict_proba(X_sample)[0]
            
            print(f"\n   Sample Application {i}:")
            print(f"   • Loan Amount: ${app['LOAN']:,}")
            print(f"   • Property Value: ${app['VALUE']:,}")
            print(f"   • Job: {app['JOB']}")
            print(f"   • Prediction: {'DEFAULT' if prediction == 1 else 'NO DEFAULT'}")
            print(f"   • Default Probability: {probability[1]:.1%}")
            print(f"   • Recommendation: {'REJECT' if prediction == 1 else 'APPROVE'}")
        
        # 7. Business metrics summary
        print("\n💼 Step 7: Business metrics summary...")
        metrics = evaluation_results['metrics']
        print(f"   • Model Accuracy: {metrics['accuracy']:.1%}")
        print(f"   • Predicted Default Rate: {metrics['predicted_default_rate']:.1%}")
        print(f"   • Predicted Approval Rate: {metrics['predicted_approval_rate']:.1%}")
        
        # Check business targets
        target_default_rate = 0.08  # 8%
        target_approval_rate = 0.70  # 70%
        
        default_rate_ok = metrics['predicted_default_rate'] <= target_default_rate
        approval_rate_ok = metrics['predicted_approval_rate'] >= target_approval_rate
        
        print(f"   • Target Default Rate (<8%): {'✅' if default_rate_ok else '❌'}")
        print(f"   • Target Approval Rate (>70%): {'✅' if approval_rate_ok else '❌'}")
        
        print("\n🎉 Quick demo completed successfully!")
        print("\n📋 Next steps:")
        print("   • Run full setup: python setup.py")
        print("   • Start API server: python src/api.py")
        print("   • Train custom model: python src/train.py")
        print("   • Make predictions: python src/predict.py --input data/sample_input.json")
        
    except Exception as e:
        logger.error(f"Demo failed: {str(e)}")
        print(f"\n❌ Demo failed: {str(e)}")
        print("\nTroubleshooting:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Check data file exists: data/loan_default.csv")
        print("3. Run setup script: python setup.py")


if __name__ == "__main__":
    quick_demo()
