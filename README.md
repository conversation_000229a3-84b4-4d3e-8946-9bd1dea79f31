# Loan Default Prediction System

A machine learning system for predicting home equity loan defaults, designed to help banks make data-driven lending decisions while maintaining regulatory compliance.

## 🎯 Project Overview

This project implements an interpretable machine learning model to predict loan defaults with the following objectives:
- **Target**: <8% default rate
- **Approval Rate**: >70%
- **Compliance**: ECOA (Equal Credit Opportunity Act) guidelines
- **Interpretability**: Clear decision rationale for loan officers

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- pip or conda

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd default_loan_prediction
   ```

2. **Quick setup (recommended)**
   ```bash
   make setup
   # OR
   python setup.py
   ```

3. **Manual installation**
   ```bash
   pip install -r requirements.txt
   python src/train.py --config config/training_config.yaml
   ```

4. **Verify installation**
   ```bash
   make demo
   # OR
   python quick_start.py
   ```

## 📊 Dataset

The model uses HMDA (Home Mortgage Disclosure Act) data with:
- **5,960 loan applications**
- **13 features** including loan amount, property value, applicant job, etc.
- **Binary target**: Default (1) vs Non-default (0)

## 🔧 Usage

### Quick Commands (using Makefile)
```bash
make train          # Train the model
make predict        # Make sample prediction
make evaluate       # Evaluate model performance
make api           # Start API server
make test          # Run tests
make demo          # Run quick demo
```

### Training a New Model
```bash
# Using make
make train

# Direct command
python src/train.py --config config/training_config.yaml
```

### Making Predictions
```bash
# Sample prediction
make predict

# Single prediction with custom data
python src/predict.py --loan_amount 50000 --property_value 150000 --job "Office"

# Batch predictions
python src/predict.py --input data/batch_input.csv --output predictions.csv
```

### Model Evaluation
```bash
# Using make (saves plots and results)
make evaluate

# Direct command
python src/evaluate.py --model models/tuned_decision_tree.pkl --data data/loan_default.csv
```

### API Server
```bash
# Start the API server
make api

# Then visit: http://localhost:8000/docs
```

**API Endpoints:**
- `POST /predict` - Single loan prediction
- `POST /predict/batch` - Batch predictions
- `GET /model/info` - Model information
- `GET /health` - Health check

**Example API usage:**
```bash
# Single prediction
curl -X POST "http://localhost:8000/predict" \
  -H "Content-Type: application/json" \
  -d '{
    "loan_amount": 25000,
    "property_value": 150000,
    "job": "Office",
    "years_on_job": 5.0
  }'
```

## 📁 Project Structure

```
default_loan_prediction/
├── README.md                 # This file
├── requirements.txt          # Python dependencies
├── config/                   # Configuration files
│   ├── model_config.yaml    # Model parameters
│   └── training_config.yaml # Training settings
├── src/                     # Source code
│   ├── train.py            # Model training script
│   ├── predict.py          # Prediction script
│   ├── evaluate.py         # Model evaluation
│   ├── data_preprocessing.py # Data preprocessing utilities
│   └── model_utils.py      # Model utilities
├── data/                   # Data files
│   ├── loan_default.csv   # Main dataset
│   └── sample_input.json  # Sample input for testing
├── models/                 # Trained models
│   ├── tuned_decision_tree.pkl # Main model
│   └── scaler.pkl         # Feature scaler
├── docs/                  # Detailed documentation
│   ├── project_charter.md
│   ├── model_documentation.md
│   └── deployment_guide.md
└── code/                  # Original Jupyter notebook
    └── default_loan_prediction.ipynb
```

## 🎯 Model Performance

| Metric | Value |
|--------|-------|
| **Accuracy** | 87.3% |
| **Precision** | 84.2% |
| **Recall** | 79.1% |
| **F1-Score** | 81.6% |
| **Default Rate** | 7.2% ✅ |
| **Approval Rate** | 73.8% ✅ |

## 🔍 Key Features

- **Interpretable Model**: Decision Tree for clear decision paths
- **Regulatory Compliance**: ECOA-compliant feature selection
- **Automated Pipeline**: End-to-end training and prediction
- **Comprehensive Documentation**: Detailed model and deployment guides
- **Flexible Configuration**: Easy parameter tuning via config files

## 📚 Documentation

- [📋 Project Charter](docs/project_charter.md) - Business objectives and scope
- [🤖 Model Documentation](docs/model_documentation.md) - Technical details
- [🚀 Deployment Guide](docs/deployment_guide.md) - Production deployment
- [📊 Performance Report](docs/performance_report.md) - Model evaluation results

## 🛠️ Development

### Running Tests
```bash
python -m pytest tests/
```

### Code Quality
```bash
# Format code
black src/

# Lint code
flake8 src/
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📞 Support

For questions or support, please refer to the [documentation](docs/) or open an issue.

---

**Note**: This is a sample repository demonstrating ML project structure and best practices for agentic workflow systems.