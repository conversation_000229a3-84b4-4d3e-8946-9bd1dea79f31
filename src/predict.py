#!/usr/bin/env python3
"""
Prediction script for loan default prediction model.
"""

import argparse
import json
import pandas as pd
import numpy as np
import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent))

from data_preprocessing import LoanDataPreprocessor
from model_utils import LoanDefaultModel
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class LoanDefaultPredictor:
    """Main predictor class for loan default prediction."""
    
    def __init__(self, model_path, preprocessor_path):
        self.model_path = model_path
        self.preprocessor_path = preprocessor_path
        self.model = None
        self.preprocessor = None
        self._load_model_and_preprocessor()
    
    def _load_model_and_preprocessor(self):
        """Load the trained model and preprocessor."""
        logger.info("Loading model and preprocessor...")
        
        # Load preprocessor
        self.preprocessor = LoanDataPreprocessor()
        self.preprocessor.load_preprocessor(self.preprocessor_path)
        
        # Load model
        self.model = LoanDefaultModel()
        self.model.load_model(self.model_path)
        
        logger.info("Model and preprocessor loaded successfully")
    
    def predict_single(self, loan_data):
        """Make prediction for a single loan application."""
        # Convert to DataFrame if it's a dictionary
        if isinstance(loan_data, dict):
            df = pd.DataFrame([loan_data])
        else:
            df = loan_data.copy()
        
        # Preprocess data
        X_processed = self.preprocessor.preprocess_prediction_data(df)
        
        # Make prediction
        prediction = self.model.predict(X_processed)[0]
        probability = self.model.predict_proba(X_processed)[0]
        
        return {
            'prediction': int(prediction),
            'default_probability': float(probability[1]),
            'approval_probability': float(probability[0]),
            'recommendation': 'REJECT' if prediction == 1 else 'APPROVE'
        }
    
    def predict_batch(self, data):
        """Make predictions for multiple loan applications."""
        logger.info(f"Making batch predictions for {len(data)} applications...")
        
        # Preprocess data
        X_processed = self.preprocessor.preprocess_prediction_data(data)
        
        # Make predictions
        predictions = self.model.predict(X_processed)
        probabilities = self.model.predict_proba(X_processed)
        
        # Create results DataFrame
        results = pd.DataFrame({
            'prediction': predictions,
            'default_probability': probabilities[:, 1],
            'approval_probability': probabilities[:, 0],
            'recommendation': ['REJECT' if p == 1 else 'APPROVE' for p in predictions]
        })
        
        # Add original data
        results = pd.concat([data.reset_index(drop=True), results], axis=1)
        
        logger.info("Batch predictions completed")
        return results


def load_input_data(input_path):
    """Load input data from file."""
    if input_path.endswith('.json'):
        with open(input_path, 'r') as f:
            data = json.load(f)
        return pd.DataFrame([data]) if isinstance(data, dict) else pd.DataFrame(data)
    elif input_path.endswith('.csv'):
        return pd.read_csv(input_path)
    else:
        raise ValueError("Input file must be JSON or CSV format")


def save_predictions(predictions, output_path):
    """Save predictions to file."""
    if output_path.endswith('.json'):
        if isinstance(predictions, pd.DataFrame):
            predictions.to_json(output_path, orient='records', indent=2)
        else:
            with open(output_path, 'w') as f:
                json.dump(predictions, f, indent=2)
    elif output_path.endswith('.csv'):
        if isinstance(predictions, pd.DataFrame):
            predictions.to_csv(output_path, index=False)
        else:
            pd.DataFrame([predictions]).to_csv(output_path, index=False)
    else:
        raise ValueError("Output file must be JSON or CSV format")


def main():
    """Main prediction function."""
    parser = argparse.ArgumentParser(description='Make loan default predictions')
    
    # Input options
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument('--input', type=str, help='Path to input file (JSON or CSV)')
    input_group.add_argument('--loan_amount', type=float, help='Loan amount for single prediction')
    
    # Single prediction parameters
    parser.add_argument('--property_value', type=float, help='Property value')
    parser.add_argument('--mortgage_due', type=float, help='Mortgage due amount')
    parser.add_argument('--reason', type=str, choices=['HomeImp', 'DebtCon'], help='Loan reason')
    parser.add_argument('--job', type=str, help='Job category')
    parser.add_argument('--years_on_job', type=float, help='Years on current job')
    parser.add_argument('--derog_reports', type=int, default=0, help='Number of derogatory reports')
    parser.add_argument('--delinq_credit_lines', type=int, default=0, help='Number of delinquent credit lines')
    parser.add_argument('--credit_line_age', type=float, help='Age of oldest credit line (months)')
    parser.add_argument('--recent_inquiries', type=int, default=0, help='Number of recent credit inquiries')
    parser.add_argument('--credit_lines', type=int, help='Number of credit lines')
    parser.add_argument('--debt_to_income', type=float, help='Debt-to-income ratio')
    
    # Model paths
    parser.add_argument('--model', type=str, default='models/tuned_decision_tree.pkl',
                       help='Path to trained model')
    parser.add_argument('--preprocessor', type=str, default='models/preprocessor.pkl',
                       help='Path to preprocessor')
    
    # Output
    parser.add_argument('--output', type=str, help='Path to save predictions')
    
    args = parser.parse_args()
    
    # Check if model files exist
    if not os.path.exists(args.model):
        logger.error(f"Model file not found: {args.model}")
        sys.exit(1)
    
    if not os.path.exists(args.preprocessor):
        logger.error(f"Preprocessor file not found: {args.preprocessor}")
        sys.exit(1)
    
    # Initialize predictor
    try:
        predictor = LoanDefaultPredictor(args.model, args.preprocessor)
    except Exception as e:
        logger.error(f"Failed to load model: {str(e)}")
        sys.exit(1)
    
    # Make predictions
    try:
        if args.input:
            # Batch prediction from file
            data = load_input_data(args.input)
            predictions = predictor.predict_batch(data)
            
            if args.output:
                save_predictions(predictions, args.output)
                logger.info(f"Predictions saved to {args.output}")
            else:
                print("\nPrediction Results:")
                print(predictions.to_string(index=False))
        
        else:
            # Single prediction from command line arguments
            loan_data = {
                'LOAN': args.loan_amount,
                'VALUE': args.property_value,
                'MORTDUE': args.mortgage_due,
                'REASON': args.reason,
                'JOB': args.job,
                'YOJ': args.years_on_job,
                'DEROG': args.derog_reports,
                'DELINQ': args.delinq_credit_lines,
                'CLAGE': args.credit_line_age,
                'NINQ': args.recent_inquiries,
                'CLNO': args.credit_lines,
                'DEBTINC': args.debt_to_income
            }
            
            # Remove None values
            loan_data = {k: v for k, v in loan_data.items() if v is not None}
            
            prediction = predictor.predict_single(loan_data)
            
            if args.output:
                save_predictions(prediction, args.output)
                logger.info(f"Prediction saved to {args.output}")
            else:
                print("\nPrediction Result:")
                print(f"Recommendation: {prediction['recommendation']}")
                print(f"Default Probability: {prediction['default_probability']:.3f}")
                print(f"Approval Probability: {prediction['approval_probability']:.3f}")
    
    except Exception as e:
        logger.error(f"Prediction failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
