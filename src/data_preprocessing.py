"""
Data preprocessing utilities for loan default prediction.
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
import joblib
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LoanDataPreprocessor:
    """Handles data preprocessing for loan default prediction."""
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.label_encoders = {}
        self.feature_columns = None
        self.target_column = 'BAD'
        
    def load_data(self, file_path):
        """Load data from CSV file."""
        logger.info(f"Loading data from {file_path}")
        data = pd.read_csv(file_path)
        logger.info(f"Loaded {len(data)} rows and {len(data.columns)} columns")
        return data
    
    def handle_missing_values(self, data):
        """Handle missing values in the dataset."""
        logger.info("Handling missing values...")
        
        # For numerical columns, fill with median
        numerical_cols = data.select_dtypes(include=[np.number]).columns
        for col in numerical_cols:
            if col != self.target_column:
                median_val = data[col].median()
                data[col].fillna(median_val, inplace=True)
        
        # For categorical columns, fill with mode
        categorical_cols = data.select_dtypes(include=['object']).columns
        for col in categorical_cols:
            mode_val = data[col].mode()[0] if not data[col].mode().empty else 'Unknown'
            data[col].fillna(mode_val, inplace=True)
            
        return data
    
    def encode_categorical_features(self, data, fit=True):
        """Encode categorical features."""
        logger.info("Encoding categorical features...")
        
        categorical_cols = data.select_dtypes(include=['object']).columns
        
        for col in categorical_cols:
            if fit:
                self.label_encoders[col] = LabelEncoder()
                data[col] = self.label_encoders[col].fit_transform(data[col])
            else:
                if col in self.label_encoders:
                    # Handle unseen categories
                    unique_values = set(data[col].unique())
                    known_values = set(self.label_encoders[col].classes_)
                    unseen_values = unique_values - known_values
                    
                    if unseen_values:
                        logger.warning(f"Unseen values in {col}: {unseen_values}")
                        # Replace unseen values with the most frequent class
                        most_frequent = self.label_encoders[col].classes_[0]
                        data[col] = data[col].replace(list(unseen_values), most_frequent)
                    
                    data[col] = self.label_encoders[col].transform(data[col])
                    
        return data
    
    def scale_features(self, X, fit=True):
        """Scale numerical features."""
        logger.info("Scaling features...")
        
        if fit:
            X_scaled = self.scaler.fit_transform(X)
        else:
            X_scaled = self.scaler.transform(X)
            
        return pd.DataFrame(X_scaled, columns=X.columns, index=X.index)
    
    def prepare_features_target(self, data):
        """Separate features and target variable."""
        if self.target_column not in data.columns:
            raise ValueError(f"Target column '{self.target_column}' not found in data")
            
        X = data.drop(columns=[self.target_column])
        y = data[self.target_column]
        
        self.feature_columns = X.columns.tolist()
        
        return X, y
    
    def preprocess_training_data(self, data):
        """Complete preprocessing pipeline for training data."""
        logger.info("Starting training data preprocessing...")
        
        # Handle missing values
        data = self.handle_missing_values(data)
        
        # Encode categorical features
        data = self.encode_categorical_features(data, fit=True)
        
        # Separate features and target
        X, y = self.prepare_features_target(data)
        
        # Scale features
        X_scaled = self.scale_features(X, fit=True)
        
        logger.info("Training data preprocessing completed")
        return X_scaled, y
    
    def preprocess_prediction_data(self, data):
        """Preprocess new data for prediction."""
        logger.info("Starting prediction data preprocessing...")
        
        # Handle missing values
        data = self.handle_missing_values(data)
        
        # Encode categorical features (without fitting)
        data = self.encode_categorical_features(data, fit=False)
        
        # Ensure all required columns are present
        if self.feature_columns:
            missing_cols = set(self.feature_columns) - set(data.columns)
            if missing_cols:
                raise ValueError(f"Missing columns: {missing_cols}")
            
            # Reorder columns to match training data
            data = data[self.feature_columns]
        
        # Scale features
        X_scaled = self.scale_features(data, fit=False)
        
        logger.info("Prediction data preprocessing completed")
        return X_scaled
    
    def save_preprocessor(self, file_path):
        """Save the preprocessor state."""
        preprocessor_state = {
            'scaler': self.scaler,
            'label_encoders': self.label_encoders,
            'feature_columns': self.feature_columns,
            'target_column': self.target_column
        }
        joblib.dump(preprocessor_state, file_path)
        logger.info(f"Preprocessor saved to {file_path}")
    
    def load_preprocessor(self, file_path):
        """Load the preprocessor state."""
        preprocessor_state = joblib.load(file_path)
        self.scaler = preprocessor_state['scaler']
        self.label_encoders = preprocessor_state['label_encoders']
        self.feature_columns = preprocessor_state['feature_columns']
        self.target_column = preprocessor_state['target_column']
        logger.info(f"Preprocessor loaded from {file_path}")


def split_data(X, y, test_size=0.2, random_state=42):
    """Split data into training and testing sets."""
    return train_test_split(X, y, test_size=test_size, random_state=random_state, stratify=y)


if __name__ == "__main__":
    # Example usage
    preprocessor = LoanDataPreprocessor()
    
    # Load and preprocess training data
    data = preprocessor.load_data("../data/loan_default.csv")
    X, y = preprocessor.preprocess_training_data(data)
    
    # Split data
    X_train, X_test, y_train, y_test = split_data(X, y)
    
    print(f"Training set: {X_train.shape}")
    print(f"Test set: {X_test.shape}")
    print(f"Feature columns: {preprocessor.feature_columns}")
