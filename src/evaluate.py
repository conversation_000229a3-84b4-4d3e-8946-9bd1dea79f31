#!/usr/bin/env python3
"""
Model evaluation script for loan default prediction.
"""

import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent))

from data_preprocessing import LoanDataPreprocessor
from model_utils import LoanDefaultModel, print_evaluation_results
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def plot_confusion_matrix(cm, save_path=None):
    """Plot confusion matrix."""
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=['No Default', 'Default'],
                yticklabels=['No Default', 'Default'])
    plt.title('Confusion Matrix')
    plt.xlabel('Predicted')
    plt.ylabel('Actual')
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Confusion matrix plot saved to {save_path}")
    else:
        plt.show()
    
    plt.close()


def plot_feature_importance(importance_df, top_n=15, save_path=None):
    """Plot feature importance."""
    if importance_df is None:
        logger.warning("Feature importance not available")
        return
    
    plt.figure(figsize=(10, 8))
    top_features = importance_df.head(top_n)
    
    sns.barplot(data=top_features, x='importance', y='feature', palette='viridis')
    plt.title(f'Top {top_n} Feature Importance')
    plt.xlabel('Importance')
    plt.ylabel('Features')
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Feature importance plot saved to {save_path}")
    else:
        plt.show()
    
    plt.close()


def plot_prediction_distribution(y_true, y_pred_proba, save_path=None):
    """Plot distribution of prediction probabilities."""
    plt.figure(figsize=(12, 5))
    
    # Subplot 1: Distribution by actual class
    plt.subplot(1, 2, 1)
    for class_label in [0, 1]:
        class_probs = y_pred_proba[y_true == class_label, 1]
        plt.hist(class_probs, bins=30, alpha=0.7, 
                label=f'Actual: {"Default" if class_label else "No Default"}')
    
    plt.xlabel('Predicted Default Probability')
    plt.ylabel('Frequency')
    plt.title('Prediction Distribution by Actual Class')
    plt.legend()
    
    # Subplot 2: Overall distribution
    plt.subplot(1, 2, 2)
    plt.hist(y_pred_proba[:, 1], bins=30, alpha=0.7, color='skyblue')
    plt.axvline(x=0.5, color='red', linestyle='--', label='Decision Threshold')
    plt.xlabel('Predicted Default Probability')
    plt.ylabel('Frequency')
    plt.title('Overall Prediction Distribution')
    plt.legend()
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Prediction distribution plot saved to {save_path}")
    else:
        plt.show()
    
    plt.close()


def calculate_business_metrics(y_true, y_pred, y_pred_proba):
    """Calculate business-specific metrics."""
    # Default and approval rates
    actual_default_rate = np.mean(y_true)
    predicted_default_rate = np.mean(y_pred)
    
    # Risk metrics at different thresholds
    thresholds = [0.3, 0.4, 0.5, 0.6, 0.7]
    threshold_metrics = []
    
    for threshold in thresholds:
        pred_at_threshold = (y_pred_proba[:, 1] >= threshold).astype(int)
        default_rate = np.mean(pred_at_threshold)
        approval_rate = 1 - default_rate
        
        # True positive rate (sensitivity) - catching actual defaults
        tpr = np.sum((pred_at_threshold == 1) & (y_true == 1)) / np.sum(y_true == 1)
        
        # False positive rate - incorrectly rejecting good loans
        fpr = np.sum((pred_at_threshold == 1) & (y_true == 0)) / np.sum(y_true == 0)
        
        threshold_metrics.append({
            'threshold': threshold,
            'predicted_default_rate': default_rate,
            'predicted_approval_rate': approval_rate,
            'true_positive_rate': tpr,
            'false_positive_rate': fpr
        })
    
    business_metrics = {
        'actual_default_rate': actual_default_rate,
        'predicted_default_rate': predicted_default_rate,
        'threshold_analysis': threshold_metrics
    }
    
    return business_metrics


def evaluate_model(model_path, preprocessor_path, test_data_path, output_dir=None):
    """Comprehensive model evaluation."""
    logger.info("Starting comprehensive model evaluation...")
    
    # Load model and preprocessor
    model = LoanDefaultModel()
    model.load_model(model_path)
    
    preprocessor = LoanDataPreprocessor()
    preprocessor.load_preprocessor(preprocessor_path)
    
    # Load test data
    logger.info(f"Loading test data from {test_data_path}")
    test_data = pd.read_csv(test_data_path)
    
    # Preprocess test data
    if 'BAD' in test_data.columns:
        X_test = test_data.drop(columns=['BAD'])
        y_test = test_data['BAD']
        X_test_processed = preprocessor.preprocess_prediction_data(X_test)
    else:
        logger.error("Test data must contain 'BAD' column for evaluation")
        return None
    
    # Make predictions
    y_pred = model.predict(X_test_processed)
    y_pred_proba = model.predict_proba(X_test_processed)
    
    # Standard evaluation
    evaluation_results = model.evaluate(X_test_processed, y_test)
    print_evaluation_results(evaluation_results)
    
    # Business metrics
    business_metrics = calculate_business_metrics(y_test, y_pred, y_pred_proba)
    
    print("\n" + "="*50)
    print("BUSINESS METRICS")
    print("="*50)
    print(f"Actual Default Rate: {business_metrics['actual_default_rate']:.1%}")
    print(f"Predicted Default Rate: {business_metrics['predicted_default_rate']:.1%}")
    
    print("\nThreshold Analysis:")
    print("Threshold | Default Rate | Approval Rate | TPR   | FPR")
    print("-" * 55)
    for metrics in business_metrics['threshold_analysis']:
        print(f"{metrics['threshold']:.1f}       | "
              f"{metrics['predicted_default_rate']:.1%}        | "
              f"{metrics['predicted_approval_rate']:.1%}         | "
              f"{metrics['true_positive_rate']:.3f} | "
              f"{metrics['false_positive_rate']:.3f}")
    
    # Feature importance
    feature_importance = model.get_feature_importance(preprocessor.feature_columns)
    if feature_importance is not None:
        print(f"\nTop 10 Most Important Features:")
        print(feature_importance.head(10).to_string(index=False))
    
    # Generate plots if output directory is specified
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        
        # Confusion matrix
        cm = evaluation_results['confusion_matrix']
        plot_confusion_matrix(cm, os.path.join(output_dir, 'confusion_matrix.png'))
        
        # Feature importance
        plot_feature_importance(feature_importance, 
                              save_path=os.path.join(output_dir, 'feature_importance.png'))
        
        # Prediction distribution
        plot_prediction_distribution(y_test, y_pred_proba,
                                   save_path=os.path.join(output_dir, 'prediction_distribution.png'))
        
        # Save detailed results
        results_summary = {
            'evaluation_metrics': evaluation_results['metrics'],
            'business_metrics': business_metrics
        }
        
        import json
        with open(os.path.join(output_dir, 'evaluation_results.json'), 'w') as f:
            json.dump(results_summary, f, indent=2, default=str)
        
        if feature_importance is not None:
            feature_importance.to_csv(os.path.join(output_dir, 'feature_importance.csv'), index=False)
        
        logger.info(f"Evaluation results saved to {output_dir}")
    
    return evaluation_results, business_metrics, feature_importance


def main():
    """Main evaluation function."""
    parser = argparse.ArgumentParser(description='Evaluate loan default prediction model')
    parser.add_argument('--model', type=str, default='models/tuned_decision_tree.pkl',
                       help='Path to trained model')
    parser.add_argument('--preprocessor', type=str, default='models/preprocessor.pkl',
                       help='Path to preprocessor')
    parser.add_argument('--data', type=str, required=True,
                       help='Path to test data CSV file')
    parser.add_argument('--output', type=str, help='Output directory for plots and results')
    
    args = parser.parse_args()
    
    # Check if files exist
    for file_path in [args.model, args.preprocessor, args.data]:
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            sys.exit(1)
    
    # Run evaluation
    try:
        results = evaluate_model(args.model, args.preprocessor, args.data, args.output)
        logger.info("Model evaluation completed successfully!")
    except Exception as e:
        logger.error(f"Evaluation failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
