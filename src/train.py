#!/usr/bin/env python3
"""
Training script for loan default prediction model.
"""

import argparse
import yaml
import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent))

from data_preprocessing import LoanDataPreprocessor, split_data
from model_utils import LoanDefaultModel, print_evaluation_results
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def load_config(config_path):
    """Load configuration from YAML file."""
    with open(config_path, 'r') as file:
        config = yaml.safe_load(file)
    return config


def train_model(config):
    """Train the loan default prediction model."""
    logger.info("Starting model training pipeline...")
    
    # Initialize preprocessor
    preprocessor = LoanDataPreprocessor()
    
    # Load and preprocess data
    data_path = config['data']['train_path']
    logger.info(f"Loading training data from {data_path}")
    data = preprocessor.load_data(data_path)
    
    # Preprocess training data
    X, y = preprocessor.preprocess_training_data(data)
    
    # Split data
    test_size = config['data'].get('test_size', 0.2)
    random_state = config['training'].get('random_state', 42)
    X_train, X_test, y_train, y_test = split_data(X, y, test_size=test_size, random_state=random_state)
    
    logger.info(f"Training set size: {X_train.shape}")
    logger.info(f"Test set size: {X_test.shape}")
    
    # Initialize model
    model_type = config['model']['type']
    model = LoanDefaultModel(model_type)
    
    # Train model
    if config['training'].get('tune_hyperparameters', False):
        logger.info("Hyperparameter tuning enabled")
        param_grid = config['model'].get('param_grid', None)
        cv_folds = config['training'].get('cv_folds', 5)
        best_params = model.tune_hyperparameters(X_train, y_train, param_grid, cv_folds)
        logger.info(f"Best parameters found: {best_params}")
    else:
        model_params = config['model'].get('params', {})
        model.train(X_train, y_train, **model_params)
    
    # Evaluate model
    logger.info("Evaluating model on test set...")
    evaluation_results = model.evaluate(X_test, y_test)
    print_evaluation_results(evaluation_results)
    
    # Feature importance (if available)
    feature_importance = model.get_feature_importance(preprocessor.feature_columns)
    if feature_importance is not None:
        logger.info("Top 10 most important features:")
        print(feature_importance.head(10))
    
    # Save model and preprocessor
    model_save_path = config['output']['model_path']
    preprocessor_save_path = config['output']['preprocessor_path']
    
    os.makedirs(os.path.dirname(model_save_path), exist_ok=True)
    os.makedirs(os.path.dirname(preprocessor_save_path), exist_ok=True)
    
    model.save_model(model_save_path)
    preprocessor.save_preprocessor(preprocessor_save_path)
    
    logger.info("Training pipeline completed successfully!")
    
    return model, preprocessor, evaluation_results


def main():
    """Main training function."""
    parser = argparse.ArgumentParser(description='Train loan default prediction model')
    parser.add_argument('--config', type=str, default='config/training_config.yaml',
                       help='Path to training configuration file')
    
    args = parser.parse_args()
    
    # Check if config file exists
    if not os.path.exists(args.config):
        logger.error(f"Configuration file not found: {args.config}")
        sys.exit(1)
    
    # Load configuration
    config = load_config(args.config)
    
    # Train model
    try:
        model, preprocessor, results = train_model(config)
        logger.info("Training completed successfully!")
    except Exception as e:
        logger.error(f"Training failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
