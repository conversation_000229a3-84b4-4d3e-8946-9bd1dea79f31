#!/usr/bin/env python3
"""
FastAPI server for loan default prediction model.
"""

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
from typing import Optional, List
import uvicorn
import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent))

from data_preprocessing import LoanDataPreprocessor
from model_utils import LoanDefaultModel
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Loan Default Prediction API",
    description="API for predicting loan default probability",
    version="1.0.0"
)

# Global variables for model and preprocessor
model = None
preprocessor = None


class LoanApplication(BaseModel):
    """Loan application data model."""
    loan_amount: float = Field(..., description="Loan amount requested", example=25000)
    mortgage_due: Optional[float] = Field(None, description="Amount due on existing mortgage", example=85000)
    property_value: Optional[float] = Field(None, description="Property value", example=150000)
    reason: Optional[str] = Field(None, description="Loan purpose", example="HomeImp")
    job: Optional[str] = Field(None, description="Job category", example="Office")
    years_on_job: Optional[float] = Field(None, description="Years on current job", example=5.0)
    derog_reports: Optional[int] = Field(0, description="Number of derogatory reports", example=0)
    delinq_credit_lines: Optional[int] = Field(0, description="Number of delinquent credit lines", example=0)
    credit_line_age: Optional[float] = Field(None, description="Age of oldest credit line (months)", example=120.5)
    recent_inquiries: Optional[int] = Field(0, description="Number of recent credit inquiries", example=1)
    credit_lines: Optional[int] = Field(None, description="Number of credit lines", example=12)
    debt_to_income: Optional[float] = Field(None, description="Debt-to-income ratio", example=35.2)


class PredictionResponse(BaseModel):
    """Prediction response model."""
    prediction: int = Field(..., description="Prediction (0=No Default, 1=Default)")
    default_probability: float = Field(..., description="Probability of default")
    approval_probability: float = Field(..., description="Probability of approval")
    recommendation: str = Field(..., description="Recommendation (APPROVE/REJECT)")


class BatchPredictionRequest(BaseModel):
    """Batch prediction request model."""
    applications: List[LoanApplication]


class BatchPredictionResponse(BaseModel):
    """Batch prediction response model."""
    predictions: List[PredictionResponse]
    summary: dict


def load_model_and_preprocessor():
    """Load the trained model and preprocessor."""
    global model, preprocessor
    
    model_path = "models/tuned_decision_tree.pkl"
    preprocessor_path = "models/preprocessor.pkl"
    
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"Model file not found: {model_path}")
    
    if not os.path.exists(preprocessor_path):
        raise FileNotFoundError(f"Preprocessor file not found: {preprocessor_path}")
    
    # Load preprocessor
    preprocessor = LoanDataPreprocessor()
    preprocessor.load_preprocessor(preprocessor_path)
    
    # Load model
    model = LoanDefaultModel()
    model.load_model(model_path)
    
    logger.info("Model and preprocessor loaded successfully")


@app.on_event("startup")
async def startup_event():
    """Load model on startup."""
    try:
        load_model_and_preprocessor()
    except Exception as e:
        logger.error(f"Failed to load model: {str(e)}")
        raise


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "Loan Default Prediction API",
        "version": "1.0.0",
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "model_loaded": model is not None,
        "preprocessor_loaded": preprocessor is not None
    }


@app.post("/predict", response_model=PredictionResponse)
async def predict_single(application: LoanApplication):
    """Make a single loan default prediction."""
    try:
        # Convert to dictionary format expected by the model
        loan_data = {
            'LOAN': application.loan_amount,
            'MORTDUE': application.mortgage_due,
            'VALUE': application.property_value,
            'REASON': application.reason,
            'JOB': application.job,
            'YOJ': application.years_on_job,
            'DEROG': application.derog_reports,
            'DELINQ': application.delinq_credit_lines,
            'CLAGE': application.credit_line_age,
            'NINQ': application.recent_inquiries,
            'CLNO': application.credit_lines,
            'DEBTINC': application.debt_to_income
        }
        
        # Remove None values
        loan_data = {k: v for k, v in loan_data.items() if v is not None}
        
        # Make prediction using the predictor logic
        import pandas as pd
        df = pd.DataFrame([loan_data])
        X_processed = preprocessor.preprocess_prediction_data(df)
        
        prediction = model.predict(X_processed)[0]
        probability = model.predict_proba(X_processed)[0]
        
        result = {
            'prediction': int(prediction),
            'default_probability': float(probability[1]),
            'approval_probability': float(probability[0]),
            'recommendation': 'REJECT' if prediction == 1 else 'APPROVE'
        }
        
        return PredictionResponse(**result)
        
    except Exception as e:
        logger.error(f"Prediction error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")


@app.post("/predict/batch", response_model=BatchPredictionResponse)
async def predict_batch(request: BatchPredictionRequest):
    """Make batch loan default predictions."""
    try:
        predictions = []
        
        for application in request.applications:
            # Convert to dictionary format
            loan_data = {
                'LOAN': application.loan_amount,
                'MORTDUE': application.mortgage_due,
                'VALUE': application.property_value,
                'REASON': application.reason,
                'JOB': application.job,
                'YOJ': application.years_on_job,
                'DEROG': application.derog_reports,
                'DELINQ': application.delinq_credit_lines,
                'CLAGE': application.credit_line_age,
                'NINQ': application.recent_inquiries,
                'CLNO': application.credit_lines,
                'DEBTINC': application.debt_to_income
            }
            
            # Remove None values
            loan_data = {k: v for k, v in loan_data.items() if v is not None}
            
            # Make prediction
            import pandas as pd
            df = pd.DataFrame([loan_data])
            X_processed = preprocessor.preprocess_prediction_data(df)
            
            prediction = model.predict(X_processed)[0]
            probability = model.predict_proba(X_processed)[0]
            
            result = PredictionResponse(
                prediction=int(prediction),
                default_probability=float(probability[1]),
                approval_probability=float(probability[0]),
                recommendation='REJECT' if prediction == 1 else 'APPROVE'
            )
            
            predictions.append(result)
        
        # Calculate summary statistics
        total_applications = len(predictions)
        approvals = sum(1 for p in predictions if p.recommendation == 'APPROVE')
        rejections = total_applications - approvals
        avg_default_prob = sum(p.default_probability for p in predictions) / total_applications
        
        summary = {
            'total_applications': total_applications,
            'approvals': approvals,
            'rejections': rejections,
            'approval_rate': approvals / total_applications,
            'rejection_rate': rejections / total_applications,
            'average_default_probability': avg_default_prob
        }
        
        return BatchPredictionResponse(predictions=predictions, summary=summary)
        
    except Exception as e:
        logger.error(f"Batch prediction error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Batch prediction failed: {str(e)}")


@app.get("/model/info")
async def model_info():
    """Get model information."""
    if model is None:
        raise HTTPException(status_code=503, detail="Model not loaded")
    
    return {
        "model_type": model.model_type,
        "is_trained": model.is_trained,
        "feature_count": len(preprocessor.feature_columns) if preprocessor else None,
        "features": preprocessor.feature_columns if preprocessor else None
    }


if __name__ == "__main__":
    uvicorn.run(
        "api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
